// Data Migration Service - localStorage to Firebase
import firebaseService from './firebaseService';

// Types for localStorage data
interface LocalPageView {
  page: string;
  timestamp: number;
  category?: string;
}

interface LocalTestSession {
  type: 'mock' | 'category';
  category?: string;
  timestamp: number;
  completed: boolean;
  score?: number;
  userName?: string;
  userEmail?: string;
  duration?: number;
}

interface LocalStudySession {
  category: string;
  timestamp: number;
  questionsViewed: number;
}

interface LocalMockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number;
  timestamp: number;
  completedAt?: unknown;
}

// Migration statistics
interface MigrationStats {
  pageViews: { total: number; migrated: number; failed: number };
  testSessions: { total: number; migrated: number; failed: number };
  studySessions: { total: number; migrated: number; failed: number };
  mockExamResults: { total: number; migrated: number; failed: number };
  totalTime: number;
  errors: string[];
}

class MigrationService {
  private migrationStats: MigrationStats = {
    pageViews: { total: 0, migrated: 0, failed: 0 },
    testSessions: { total: 0, migrated: 0, failed: 0 },
    studySessions: { total: 0, migrated: 0, failed: 0 },
    mockExamResults: { total: 0, migrated: 0, failed: 0 },
    totalTime: 0,
    errors: []
  };

  // Check if migration is needed
  public checkMigrationNeeded(): boolean {
    const pageViews = this.getLocalStorageData<LocalPageView[]>('driving_theory_page_views', []);
    const testSessions = this.getLocalStorageData<LocalTestSession[]>('driving_theory_test_sessions', []);
    const studySessions = this.getLocalStorageData<LocalStudySession[]>('driving_theory_study_sessions', []);
    const mockResults = this.getLocalStorageData<LocalMockExamResult[]>('mock_exam_results', []);

    const totalRecords = pageViews.length + testSessions.length + studySessions.length + mockResults.length;
    
    console.log(`📊 Found ${totalRecords} records in localStorage:`, {
      pageViews: pageViews.length,
      testSessions: testSessions.length,
      studySessions: studySessions.length,
      mockResults: mockResults.length
    });

    return totalRecords > 0;
  }

  // Get data from localStorage
  private getLocalStorageData<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
      return defaultValue;
    }
  }

  // Migrate all data
  public async migrateAllData(onProgress?: (progress: number, message: string) => void): Promise<MigrationStats> {
    const startTime = Date.now();
    console.log('🚀 Starting data migration to Firebase...');

    try {
      // Initialize Firebase if not already done
      await firebaseService.initializeDatabase();

      // Step 1: Migrate Page Views
      onProgress?.(10, 'กำลังย้ายข้อมูลการเข้าชม...');
      await this.migratePageViews();

      // Step 2: Migrate Study Sessions
      onProgress?.(30, 'กำลังย้ายข้อมูลการศึกษา...');
      await this.migrateStudySessions();

      // Step 3: Migrate Test Sessions
      onProgress?.(60, 'กำลังย้ายข้อมูลการทำข้อสอบ...');
      await this.migrateTestSessions();

      // Step 4: Migrate Mock Exam Results
      onProgress?.(80, 'กำลังย้ายข้อมูลอันดับคะแนน...');
      await this.migrateMockExamResults();

      // Step 5: Create backup and cleanup
      onProgress?.(95, 'กำลังสำรองข้อมูลและทำความสะอาด...');
      await this.createBackupAndCleanup();

      onProgress?.(100, 'การย้ายข้อมูลเสร็จสมบูรณ์!');

      this.migrationStats.totalTime = Date.now() - startTime;
      
      console.log('✅ Migration completed successfully!', this.migrationStats);
      return this.migrationStats;

    } catch (error) {
      console.error('❌ Migration failed:', error);
      this.migrationStats.errors.push(`Migration failed: ${error}`);
      this.migrationStats.totalTime = Date.now() - startTime;
      throw error;
    }
  }

  // Migrate page views
  private async migratePageViews(): Promise<void> {
    const pageViews = this.getLocalStorageData<LocalPageView[]>('driving_theory_page_views', []);
    this.migrationStats.pageViews.total = pageViews.length;

    console.log(`📄 Migrating ${pageViews.length} page views...`);

    for (const pageView of pageViews) {
      try {
        await firebaseService.savePageView({
          page: pageView.page,
          category: pageView.category,
          userAgent: 'Migrated from localStorage'
        });
        this.migrationStats.pageViews.migrated++;
      } catch (error) {
        console.error('Error migrating page view:', error);
        this.migrationStats.pageViews.failed++;
        this.migrationStats.errors.push(`Page view migration error: ${error}`);
      }
    }

    console.log(`✅ Page views migrated: ${this.migrationStats.pageViews.migrated}/${this.migrationStats.pageViews.total}`);
  }

  // Migrate study sessions
  private async migrateStudySessions(): Promise<void> {
    const studySessions = this.getLocalStorageData<LocalStudySession[]>('driving_theory_study_sessions', []);
    this.migrationStats.studySessions.total = studySessions.length;

    console.log(`📚 Migrating ${studySessions.length} study sessions...`);

    for (const session of studySessions) {
      try {
        await firebaseService.saveStudySession({
          category: session.category,
          questionsViewed: session.questionsViewed,
          userName: 'Migrated User'
        });
        this.migrationStats.studySessions.migrated++;
      } catch (error) {
        console.error('Error migrating study session:', error);
        this.migrationStats.studySessions.failed++;
        this.migrationStats.errors.push(`Study session migration error: ${error}`);
      }
    }

    console.log(`✅ Study sessions migrated: ${this.migrationStats.studySessions.migrated}/${this.migrationStats.studySessions.total}`);
  }

  // Migrate test sessions
  private async migrateTestSessions(): Promise<void> {
    const testSessions = this.getLocalStorageData<LocalTestSession[]>('driving_theory_test_sessions', []);
    this.migrationStats.testSessions.total = testSessions.length;

    console.log(`📝 Migrating ${testSessions.length} test sessions...`);

    for (const session of testSessions) {
      try {
        await firebaseService.saveTestSession({
          type: session.type,
          category: session.category,
          completed: session.completed,
          score: session.score,
          userName: session.userName || 'Migrated User',
          userEmail: session.userEmail || 'migrated@localhost',
          duration: session.duration || 0,
          questionsTotal: session.type === 'mock' ? 50 : 30,
          correctAnswers: session.score ? Math.round((session.score / 100) * (session.type === 'mock' ? 50 : 30)) : 0
        });
        this.migrationStats.testSessions.migrated++;
      } catch (error) {
        console.error('Error migrating test session:', error);
        this.migrationStats.testSessions.failed++;
        this.migrationStats.errors.push(`Test session migration error: ${error}`);
      }
    }

    console.log(`✅ Test sessions migrated: ${this.migrationStats.testSessions.migrated}/${this.migrationStats.testSessions.total}`);
  }

  // Migrate mock exam results
  private async migrateMockExamResults(): Promise<void> {
    const mockResults = this.getLocalStorageData<LocalMockExamResult[]>('mock_exam_results', []);
    this.migrationStats.mockExamResults.total = mockResults.length;

    console.log(`🏆 Migrating ${mockResults.length} mock exam results...`);

    for (const result of mockResults) {
      try {
        await firebaseService.saveMockExamResult({
          userName: result.userName || 'Migrated User',
          userEmail: result.userEmail || 'migrated@localhost',
          score: result.score,
          duration: result.duration,
          questionsTotal: 50,
          correctAnswers: Math.round((result.score / 100) * 50)
        });
        this.migrationStats.mockExamResults.migrated++;
      } catch (error) {
        console.error('Error migrating mock exam result:', error);
        this.migrationStats.mockExamResults.failed++;
        this.migrationStats.errors.push(`Mock exam result migration error: ${error}`);
      }
    }

    console.log(`✅ Mock exam results migrated: ${this.migrationStats.mockExamResults.migrated}/${this.migrationStats.mockExamResults.total}`);
  }

  // Create backup and cleanup
  private async createBackupAndCleanup(): Promise<void> {
    try {
      // Create backup of all localStorage data
      const backup = {
        timestamp: Date.now(),
        data: {
          pageViews: this.getLocalStorageData('driving_theory_page_views', []),
          testSessions: this.getLocalStorageData('driving_theory_test_sessions', []),
          studySessions: this.getLocalStorageData('driving_theory_study_sessions', []),
          mockResults: this.getLocalStorageData('mock_exam_results', [])
        },
        migrationStats: this.migrationStats
      };

      // Save backup to localStorage with timestamp
      localStorage.setItem(`migration_backup_${Date.now()}`, JSON.stringify(backup));

      // Mark migration as completed
      localStorage.setItem('firebase_migration_completed', JSON.stringify({
        completed: true,
        timestamp: Date.now(),
        stats: this.migrationStats
      }));

      console.log('💾 Backup created and migration marked as completed');

    } catch (error) {
      console.error('Error creating backup:', error);
      this.migrationStats.errors.push(`Backup creation error: ${error}`);
    }
  }

  // Check if migration was already completed
  public isMigrationCompleted(): boolean {
    try {
      const migrationStatus = localStorage.getItem('firebase_migration_completed');
      if (migrationStatus) {
        const status = JSON.parse(migrationStatus);
        return status.completed === true;
      }
      return false;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  // Get migration statistics
  public getMigrationStats(): MigrationStats {
    return this.migrationStats;
  }

  // Reset migration status (for testing)
  public resetMigrationStatus(): void {
    localStorage.removeItem('firebase_migration_completed');
    console.log('🔄 Migration status reset');
  }
}

// Export singleton instance
export const migrationService = new MigrationService();
export default migrationService;
