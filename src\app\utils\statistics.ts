// Firebase imports
import { db } from '../config/firebase';
import {
  collection,
  addDoc,
  getDocs,
  query,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';

// Statistics tracking utility
export interface PageView {
  page: string;
  timestamp: number;
  category?: string;
}

export interface TestSession {
  type: 'mock' | 'category';
  category?: string;
  timestamp: number;
  completed: boolean;
  score?: number;
  userName?: string;
  userEmail?: string;
  duration?: number; // in seconds
}

export interface StudySession {
  category: string;
  timestamp: number;
  questionsViewed: number;
}

export interface MockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number; // in seconds
  timestamp: number;
  completedAt: Timestamp;
}

export interface Statistics {
  totalPageViews: number;
  totalTestTakers: number;
  totalStudyViews: number;
  mockExamTestTakers: number;
  mostPopularPage: string;
  mostPopularCategory: string;
  categoryStats: Array<{
    name: string;
    testTakers: number;
    studyViews: number;
  }>;
  pageViews: Array<{
    page: string;
    views: number;
  }>;
  mockExamLeaderboard: MockExamResult[];
}

// Firebase collection names
const COLLECTIONS = {
  PAGE_VIEWS: 'pageViews',
  TEST_SESSIONS: 'testSessions',
  STUDY_SESSIONS: 'studySessions',
  MOCK_EXAM_RESULTS: 'mockExamResults',
};

// Storage keys (for backward compatibility and session tracking)
const STORAGE_KEYS = {
  PAGE_VIEWS: 'driving_theory_page_views',
  TEST_SESSIONS: 'driving_theory_test_sessions',
  STUDY_SESSIONS: 'driving_theory_study_sessions',
  SESSION_TRACKING: 'driving_theory_session_tracking',
};

// Session tracking to prevent duplicate tracking
const SESSION_ID = Date.now().toString();
const getSessionKey = (type: string, identifier: string) => `${type}_${identifier}_${SESSION_ID}`;

const hasTrackedInSession = (type: string, identifier: string): boolean => {
  try {
    const sessionTracking = getStoredData<string[]>(STORAGE_KEYS.SESSION_TRACKING, []);
    const sessionKey = getSessionKey(type, identifier);
    return sessionTracking.includes(sessionKey);
  } catch {
    return false;
  }
};

const markTrackedInSession = (type: string, identifier: string) => {
  try {
    const sessionTracking = getStoredData<string[]>(STORAGE_KEYS.SESSION_TRACKING, []);
    const sessionKey = getSessionKey(type, identifier);
    if (!sessionTracking.includes(sessionKey)) {
      sessionTracking.push(sessionKey);
      localStorage.setItem(STORAGE_KEYS.SESSION_TRACKING, JSON.stringify(sessionTracking));
    }
  } catch (error) {
    console.error('Error marking session tracking:', error);
  }
};

// Firebase functions
export const saveMockExamResult = async (result: Omit<MockExamResult, 'id' | 'completedAt'>): Promise<void> => {
  try {
    await addDoc(collection(db, COLLECTIONS.MOCK_EXAM_RESULTS), {
      ...result,
      completedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error saving mock exam result to Firebase:', error);
    // Fallback to localStorage
    const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
    localResults.push({
      ...result,
      id: Date.now().toString(),
      completedAt: Timestamp.now(),
    });
    localStorage.setItem('mock_exam_results', JSON.stringify(localResults));
  }
};

export const getMockExamLeaderboard = async (): Promise<MockExamResult[]> => {
  try {
    const q = query(
      collection(db, COLLECTIONS.MOCK_EXAM_RESULTS),
      orderBy('score', 'desc'),
      orderBy('duration', 'asc'),
      limit(10)
    );
    const querySnapshot = await getDocs(q);
    const results: MockExamResult[] = [];

    querySnapshot.forEach((doc) => {
      results.push({
        id: doc.id,
        ...doc.data(),
      } as MockExamResult);
    });

    return results;
  } catch (error) {
    console.error('Error fetching leaderboard from Firebase:', error);
    // Fallback to localStorage
    const localResults = getStoredData<MockExamResult[]>('mock_exam_results', []);
    return localResults
      .sort((a, b) => {
        if (b.score !== a.score) return b.score - a.score;
        return a.duration - b.duration;
      })
      .slice(0, 10);
  }
};

// Track page view (only once per session per page)
export const trackPageView = (page: string, category?: string) => {
  try {
    const identifier = `${page}_${category || 'no-category'}`;

    // Check if already tracked in this session
    if (hasTrackedInSession('page_view', identifier)) {
      return; // Skip tracking if already tracked in this session
    }

    const pageViews = getStoredData<PageView[]>(STORAGE_KEYS.PAGE_VIEWS, []);
    const newPageView: PageView = {
      page,
      timestamp: Date.now(),
      category,
    };
    pageViews.push(newPageView);
    localStorage.setItem(STORAGE_KEYS.PAGE_VIEWS, JSON.stringify(pageViews));

    // Mark as tracked in this session
    markTrackedInSession('page_view', identifier);
  } catch (error) {
    console.error('Error tracking page view:', error);
  }
};

// Track test session
export const trackTestSession = async (
  type: 'mock' | 'category',
  category?: string,
  completed: boolean = false,
  score?: number,
  userName?: string,
  userEmail?: string,
  duration?: number
) => {
  try {
    // Only prevent duplicate tracking for the same action within a short time window (5 seconds)
    const recentSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
    const fiveSecondsAgo = Date.now() - 5000;
    const recentDuplicate = recentSessions.find(session =>
      session.type === type &&
      session.category === category &&
      session.completed === completed &&
      session.timestamp > fiveSecondsAgo
    );

    if (recentDuplicate) {
      return; // Skip if same action was tracked within last 5 seconds
    }

    const testSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
    const newTestSession: TestSession = {
      type,
      category,
      timestamp: Date.now(),
      completed,
      score,
      userName,
      userEmail,
      duration,
    };
    testSessions.push(newTestSession);
    localStorage.setItem(STORAGE_KEYS.TEST_SESSIONS, JSON.stringify(testSessions));

    // If it's a completed mock exam, save to Firebase leaderboard
    if (type === 'mock' && completed && score !== undefined && userName && userEmail && duration !== undefined) {
      await saveMockExamResult({
        userName,
        userEmail,
        score,
        duration,
        timestamp: Date.now(),
      });
    }
  } catch (error) {
    console.error('Error tracking test session:', error);
  }
};

// Track study session (only once per session per category)
export const trackStudySession = (category: string, questionsViewed: number) => {
  try {
    const identifier = `${category}_${questionsViewed}`;

    // Check if already tracked in this session
    if (hasTrackedInSession('study_session', identifier)) {
      return; // Skip tracking if already tracked in this session
    }

    const studySessions = getStoredData<StudySession[]>(STORAGE_KEYS.STUDY_SESSIONS, []);
    const newStudySession: StudySession = {
      category,
      timestamp: Date.now(),
      questionsViewed,
    };
    studySessions.push(newStudySession);
    localStorage.setItem(STORAGE_KEYS.STUDY_SESSIONS, JSON.stringify(studySessions));

    // Mark as tracked in this session
    markTrackedInSession('study_session', identifier);
  } catch (error) {
    console.error('Error tracking study session:', error);
  }
};

// Get stored data with fallback
const getStoredData = <T>(key: string, fallback: T): T => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : fallback;
  } catch (error) {
    console.error(`Error getting stored data for ${key}:`, error);
    return fallback;
  }
};

// Calculate statistics from stored data
export const calculateStatistics = async (): Promise<Statistics> => {
  try {
    const pageViews = getStoredData<PageView[]>(STORAGE_KEYS.PAGE_VIEWS, []);
    const testSessions = getStoredData<TestSession[]>(STORAGE_KEYS.TEST_SESSIONS, []);
    const studySessions = getStoredData<StudySession[]>(STORAGE_KEYS.STUDY_SESSIONS, []);

    // Calculate page view stats
    const pageViewCounts: { [key: string]: number } = {};
    pageViews.forEach(pv => {
      pageViewCounts[pv.page] = (pageViewCounts[pv.page] || 0) + 1;
    });

    const sortedPageViews = Object.entries(pageViewCounts)
      .map(([page, views]) => ({ page, views }))
      .sort((a, b) => b.views - a.views);

    // Calculate category stats and mock exam stats
    const categoryStats: { [key: string]: { testTakers: number; studyViews: number } } = {};
    let mockExamTestTakers = 0;

    // Count test takers by category and mock exams (now only tracking completed tests)
    testSessions.forEach(ts => {
      if (ts.type === 'mock') {
        mockExamTestTakers++;
      } else if (ts.category) {
        if (!categoryStats[ts.category]) {
          categoryStats[ts.category] = { testTakers: 0, studyViews: 0 };
        }
        categoryStats[ts.category].testTakers++;
      }
    });

    // Count study views by category
    studySessions.forEach(ss => {
      if (!categoryStats[ss.category]) {
        categoryStats[ss.category] = { testTakers: 0, studyViews: 0 };
      }
      categoryStats[ss.category].studyViews++;
    });

    // Find most popular category
    const categoryTotals = Object.entries(categoryStats).map(([name, stats]) => ({
      name,
      total: stats.testTakers + stats.studyViews,
    }));
    const mostPopularCategory = categoryTotals.length > 0
      ? categoryTotals.sort((a, b) => b.total - a.total)[0].name
      : 'ไม่มีข้อมูล';

    // Get mock exam leaderboard
    const mockExamLeaderboard = await getMockExamLeaderboard();

    return {
      totalPageViews: pageViews.length,
      totalTestTakers: testSessions.length, // Now only tracking completed tests
      totalStudyViews: studySessions.length,
      mockExamTestTakers: mockExamTestTakers, // Add mock exam test takers
      mostPopularPage: sortedPageViews.length > 0 ? sortedPageViews[0].page : 'ไม่มีข้อมูล',
      mostPopularCategory,
      categoryStats: Object.entries(categoryStats).map(([name, stats]) => ({
        name,
        testTakers: stats.testTakers,
        studyViews: stats.studyViews,
      })).sort((a, b) => (b.testTakers + b.studyViews) - (a.testTakers + a.studyViews)),
      pageViews: sortedPageViews,
      mockExamLeaderboard,
    };
  } catch (error) {
    console.error('Error calculating statistics:', error);
    // Return empty statistics on error
    return {
      totalPageViews: 0,
      totalTestTakers: 0,
      totalStudyViews: 0,
      mockExamTestTakers: 0,
      mostPopularPage: 'ไม่มีข้อมูล',
      mostPopularCategory: 'ไม่มีข้อมูล',
      categoryStats: [],
      pageViews: [],
      mockExamLeaderboard: [],
    };
  }
};

// Clear all statistics (for testing/reset purposes)
export const clearStatistics = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.PAGE_VIEWS);
    localStorage.removeItem(STORAGE_KEYS.TEST_SESSIONS);
    localStorage.removeItem(STORAGE_KEYS.STUDY_SESSIONS);
    localStorage.removeItem(STORAGE_KEYS.SESSION_TRACKING);
  } catch (error) {
    console.error('Error clearing statistics:', error);
  }
};
