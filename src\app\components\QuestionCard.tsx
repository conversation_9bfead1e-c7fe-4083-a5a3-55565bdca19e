'use client';

import Image from 'next/image';
import { Question } from '../data';

interface QuestionCardProps {
  question: Question;
  questionNumber: number;
  selectedAnswer?: number;
  onAnswerSelect: (answerIndex: number) => void;
}

export default function QuestionCard({ 
  question, 
  questionNumber, 
  selectedAnswer, 
  onAnswerSelect 
}: QuestionCardProps) {
  
  const renderQuestionContent = () => {
    return question.question.map((item, index) => (
      <div key={index} className="mb-4">
        {item.text && (
          <p className="text-gray-800 leading-relaxed">{item.text}</p>
        )}
        {item.image && (
          <div className="mt-4 flex justify-center">
            <div className="relative max-w-md w-full">
              <Image
                src={item.image}
                alt={`Question ${questionNumber} image`}
                width={300}
                height={200}
                className="rounded-lg shadow-md object-contain w-full h-auto"
                unoptimized
              />
            </div>
          </div>
        )}
      </div>
    ));
  };

  const renderOptions = () => {
    return question.options.map((option, index) => (
      <button
        key={index}
        onClick={() => onAnswerSelect(index)}
        className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
          selectedAnswer === index
            ? 'border-blue-500 bg-blue-50 text-blue-800'
            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
        }`}
      >
        <div className="flex items-start space-x-3">
          <div className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-semibold ${
            selectedAnswer === index
              ? 'border-blue-500 bg-blue-500 text-white'
              : 'border-gray-400 text-gray-400'
          }`}>
            {String.fromCharCode(65 + index)}
          </div>
          <div className="flex-1">
            {option.text && (
              <p className="text-gray-800 leading-relaxed">{option.text}</p>
            )}
            {option.image && (
              <div className="mt-2">
                <Image
                  src={option.image}
                  alt={`Option ${String.fromCharCode(65 + index)}`}
                  width={200}
                  height={150}
                  className="rounded-md shadow-sm object-contain"
                  unoptimized
                />
              </div>
            )}
          </div>
        </div>
      </button>
    ));
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 animate-fade-in">
      {/* Question Header */}
      <div className="mb-4 md:mb-6">
        <h2 className="text-lg md:text-xl font-bold text-gray-800 mb-3 md:mb-4">
          {question.numbers}
        </h2>
        <div className="bg-gray-50 rounded-lg p-3 md:p-4">
          {renderQuestionContent()}
        </div>
      </div>

      {/* Answer Options */}
      <div className="space-y-2 md:space-y-3">
        <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-3 md:mb-4">
          เลือกคำตอบ:
        </h3>
        {renderOptions()}
      </div>

      {/* Answer Status */}
      <div className="mt-4 md:mt-6 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          {selectedAnswer !== undefined
            ? `คุณเลือกคำตอบ: ${String.fromCharCode(65 + selectedAnswer)}`
            : 'กรุณาเลือกคำตอบ'
          }
        </p>
      </div>
    </div>
  );
}
