import type { Metada<PERSON> } from "next";
import {<PERSON>, <PERSON>o_Mono} from "next/font/google";
import "./globals.css";

const geistSans = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

const geistMono = Roboto_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  weight: ["100", "200", "300", "400", "500", "600", "700"],
});



export const metadata: Metadata = {
  title: "ข้อสอบใบขับขี่ - ระบบทดสอบออนไลน์",
  description: "ทดสอบข้อสอบใบขับขี่ออนไลน์ พร้อมข้อสอบเสมือนจริงและข้อสอบตามหมวดหมู่",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 min-h-screen`}
      >
        <div className="min-h-screen flex flex-col ">
          <header className="bg-blue-600 text-white shadow-lg">
            <div className="container mx-auto px-4 py-4">
              <h1 className="text-2xl font-bold text-center">
                ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์
              </h1>
            </div>
          </header>
          <main className="flex-1 container mx-auto px-4 py-8">
            {children}
          </main>
          <footer className="bg-gray-800 text-white py-4 ">
            <div className="container mx-auto px-4 text-center">
              <p>&copy; 2025 ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์ By SiNgH-KhA</p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
