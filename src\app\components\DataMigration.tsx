'use client';

import { useState, useEffect } from 'react';
import migrationService from '../services/migrationService';

interface MigrationStats {
  pageViews: { total: number; migrated: number; failed: number };
  testSessions: { total: number; migrated: number; failed: number };
  studySessions: { total: number; migrated: number; failed: number };
  mockExamResults: { total: number; migrated: number; failed: number };
  totalTime: number;
  errors: string[];
}

export default function DataMigration() {
  const [showMigration, setShowMigration] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [, setNeedsMigration] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState(0);
  const [migrationMessage, setMigrationMessage] = useState('');
  const [migrationStats, setMigrationStats] = useState<MigrationStats | null>(null);
  const [migrationCompleted, setMigrationCompleted] = useState(false);
  const [showStats, setShowStats] = useState(false);

  useEffect(() => {
    checkMigrationStatus();
  }, []);

  const checkMigrationStatus = async () => {
    try {
      setIsChecking(true);
      
      // Check if migration was already completed
      const isCompleted = migrationService.isMigrationCompleted();
      if (isCompleted) {
        setMigrationCompleted(true);
        setIsChecking(false);
        return;
      }

      // Check if migration is needed
      const needsIt = migrationService.checkMigrationNeeded();
      setNeedsMigration(needsIt);
      setShowMigration(needsIt);
      
    } catch (error) {
      console.error('Error checking migration status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const startMigration = async () => {
    try {
      setIsMigrating(true);
      setMigrationProgress(0);
      setMigrationMessage('เริ่มต้นการย้ายข้อมูล...');

      const stats = await migrationService.migrateAllData((progress, message) => {
        setMigrationProgress(progress);
        setMigrationMessage(message);
      });

      setMigrationStats(stats);
      setMigrationCompleted(true);
      setShowMigration(false);
      
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrationMessage(`การย้ายข้อมูลล้มเหลว: ${error}`);
    } finally {
      setIsMigrating(false);
    }
  };

  const skipMigration = () => {
    setShowMigration(false);
    // Mark as completed to not show again
    localStorage.setItem('firebase_migration_completed', JSON.stringify({
      completed: true,
      timestamp: Date.now(),
      skipped: true
    }));
  };

  const resetMigration = () => {
    migrationService.resetMigrationStatus();
    setMigrationCompleted(false);
    setShowMigration(false);
    setMigrationStats(null);
    checkMigrationStatus();
  };

  // Don't show anything if checking or no migration needed
  if (isChecking || (!showMigration && !migrationCompleted)) {
    return null;
  }

  // Migration completed - show success message
  if (migrationCompleted && !showStats) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-6xl mb-4">✅</div>
            <h2 className="text-2xl font-bold text-green-600 mb-2">
              ย้ายข้อมูลสำเร็จ!
            </h2>
            <p className="text-gray-600 mb-4">
              ข้อมูลทั้งหมดได้ถูกย้ายไปยัง Firebase เรียบร้อยแล้ว
            </p>
            <div className="flex gap-2 justify-center">
              <button
                onClick={() => setShowStats(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                ดูรายละเอียด
              </button>
              <button
                onClick={() => setMigrationCompleted(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                ปิด
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show migration stats
  if (showStats && migrationStats) {
    const totalMigrated = migrationStats.pageViews.migrated + migrationStats.testSessions.migrated +
                         migrationStats.studySessions.migrated + migrationStats.mockExamResults.migrated;
    const totalFailed = migrationStats.pageViews.failed + migrationStats.testSessions.failed +
                       migrationStats.studySessions.failed + migrationStats.mockExamResults.failed;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">📊 รายงานการย้ายข้อมูล</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-green-50 p-3 rounded">
              <div className="text-2xl font-bold text-green-600">{totalMigrated}</div>
              <div className="text-sm text-gray-600">ย้ายสำเร็จ</div>
            </div>
            <div className="bg-red-50 p-3 rounded">
              <div className="text-2xl font-bold text-red-600">{totalFailed}</div>
              <div className="text-sm text-gray-600">ย้ายไม่สำเร็จ</div>
            </div>
          </div>

          <div className="space-y-3 mb-4">
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>📄 การเข้าชมหน้าเว็บ</span>
              <span className="text-sm">
                {migrationStats.pageViews.migrated}/{migrationStats.pageViews.total}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>📝 การทำข้อสอบ</span>
              <span className="text-sm">
                {migrationStats.testSessions.migrated}/{migrationStats.testSessions.total}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>📚 การศึกษา</span>
              <span className="text-sm">
                {migrationStats.studySessions.migrated}/{migrationStats.studySessions.total}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>🏆 อันดับคะแนน</span>
              <span className="text-sm">
                {migrationStats.mockExamResults.migrated}/{migrationStats.mockExamResults.total}
              </span>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            ⏱️ เวลาที่ใช้: {(migrationStats.totalTime / 1000).toFixed(2)} วินาที
          </div>

          {migrationStats.errors.length > 0 && (
            <div className="mb-4">
              <h3 className="font-semibold text-red-600 mb-2">ข้อผิดพลาด:</h3>
              <div className="bg-red-50 p-2 rounded text-sm max-h-20 overflow-y-auto">
                {migrationStats.errors.map((error, index) => (
                  <div key={index} className="text-red-700">{error}</div>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-2 justify-end">
            <button
              onClick={resetMigration}
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
            >
              ย้ายใหม่
            </button>
            <button
              onClick={() => setShowStats(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ปิด
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show migration dialog
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        {!isMigrating ? (
          <div className="text-center">
            <div className="text-6xl mb-4">🔄</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              ย้ายข้อมูลไปยัง Firebase
            </h2>
            <p className="text-gray-600 mb-4">
              พบข้อมูลในเครื่องของคุณ ต้องการย้ายไปยัง Firebase เพื่อการใช้งานที่ดีขึ้นหรือไม่?
            </p>
            <div className="bg-blue-50 p-3 rounded mb-4 text-sm">
              <div className="font-semibold text-blue-800 mb-1">ประโยชน์ของการย้ายข้อมูล:</div>
              <ul className="text-blue-700 text-left space-y-1">
                <li>• ข้อมูลปลอดภัยและไม่สูญหาย</li>
                <li>• อันดับคะแนนแบบ Real-time</li>
                <li>• สถิติที่แม่นยำและครบถ้วน</li>
                <li>• ใช้งานได้หลายอุปกรณ์</li>
              </ul>
            </div>
            <div className="flex gap-2 justify-center">
              <button
                onClick={startMigration}
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 font-semibold"
              >
                ย้ายข้อมูล
              </button>
              <button
                onClick={skipMigration}
                className="px-6 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                ข้าม
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-4xl mb-4">⏳</div>
            <h2 className="text-xl font-bold text-gray-800 mb-4">
              กำลังย้ายข้อมูล...
            </h2>
            <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${migrationProgress}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-600 mb-2">
              {migrationProgress}% เสร็จสิ้น
            </div>
            <div className="text-sm text-blue-600">
              {migrationMessage}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
