'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { categories, getCategoryById } from '../data';
import Image from 'next/image';
import { FaAngleDoubleLeft, FaAngleLeft, FaAngleRight, FaAngleDoubleRight } from 'react-icons/fa';

export default function StudyPage() {
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
  }, [searchParams]);

  const category = selectedCategory ? getCategoryById(selectedCategory) : null;
  const questions = category?.data || [];
  const currentQuestion = questions[currentQuestionIndex];



  useEffect(() => {
    // Update input value when current question changes
    setInputValue((currentQuestionIndex + 1).toString());
  }, [currentQuestionIndex]);

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setCurrentQuestionIndex(0);
  };

  // New navigation handlers
  const handleFirst = () => {
    setCurrentQuestionIndex(0);
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handleLast = () => {
    setCurrentQuestionIndex(questions.length - 1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const questionNumber = parseInt(inputValue);
      if (questionNumber >= 1 && questionNumber <= questions.length) {
        setCurrentQuestionIndex(questionNumber - 1);
      }
    }
  };

  if (!selectedCategory) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 animate-fade-in">
          <h1 className="text-2xl md:text-4xl font-bold text-gray-800 mb-4">
            📖 ศึกษาข้อสอบและคำตอบ
          </h1>
          <p className="text-base md:text-lg text-gray-600 mb-6 px-4">
            เลือกหมวดหมู่ที่ต้องการศึกษาข้อสอบและคำตอบ
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">
            เลือกหมวดหมู่ข้อสอบ
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className="bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center">
                  <p className="text-xs text-blue-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-green-600 font-medium">
                    📚 ศึกษา
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="text-center mt-8">
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            🏠 กลับหน้าหลัก
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 mb-4 md:mb-6 animate-fade-in">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-xl md:text-2xl font-bold text-gray-800 mb-1">
              📖 {category?.name}
            </h1>
            <p className="text-sm md:text-base text-gray-600">
              {category?.description}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedCategory(null)}
              className="bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
            >
              เลือกหมวดหมู่อื่น
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              🏠 หน้าหลัก
            </button>
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${questions.length > 0 ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0}%` }}
          ></div>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:justify-between text-xs md:text-sm text-gray-600 gap-2">
          <span>ข้อ {currentQuestionIndex + 1} จาก {questions.length}</span>
          <span>หมวดหมู่: {category?.name}</span>
        </div>
      </div>



      {currentQuestion && (
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 animate-fade-in">
              <div className="">
                <h2 className="text-lg md:text-xl font-bold text-gray-800 mb-2">
                  {currentQuestion.numbers}
                </h2>
                <div className="bg-gray-50 rounded-lg p-3 md:p-4">
                  {currentQuestion.question.map((item, index) => (
                    <div key={index} className="">
                      {item.text && (
                        <p className="text-gray-800 text-lg md:text-md leading-relaxed">{item.text}</p>
                      )}
                      {item.image && (
                        <div className="my-4 flex justify-center">
                          <div className="relative max-w-md w-full">
                            <Image
                              src={item.image}
                              alt={`Question ${currentQuestionIndex + 1} image`}
                              width={200}
                              height={200}
                              className="rounded-lg shadow-md object-contain w-full h-auto"
                              unoptimized
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2 md:space-y-3 mt-4">
                <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-3 md:mb-4">
                  ตัวเลือก:
                </h3>
                {currentQuestion.options.map((option, index) => {
                  const isCorrectAnswer = currentQuestion.correctAnswer === index;

                  return (
                    <div
                      key={index}
                      className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
                        isCorrectAnswer
                          ? 'border-green-500 bg-green-50 text-green-800'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-semibold ${
                          isCorrectAnswer
                            ? 'border-green-500 bg-green-500 text-white'
                            : 'border-gray-400 text-gray-400'
                        }`}>
                          {String.fromCharCode(65 + index)}
                        </div>
                        <div className="flex-1">
                          {option.text && (
                            <p className="text-gray-800 leading-relaxed">{option.text}</p>
                          )}
                          {option.image && (
                            <div className="mt-2">
                              <Image
                                src={option.image}
                                alt={`Option ${String.fromCharCode(65 + index)}`}
                                width={200}
                                height={150}
                                className="rounded-md shadow-sm object-contain"
                                unoptimized
                              />
                            </div>
                          )}
                        </div>
                        {isCorrectAnswer && (
                          <div className="flex-shrink-0 text-green-600 font-bold text-lg">
                            ✓
                          </div>
                        )}
                      </div>


                    </div>
                  );
                })}
              </div>



              <div className="mt-6 bg-gray-100 rounded-lg shadow-md py-3 flex items-center justify-center">
                <div className="flex items-center justify-center space-x-4">
                  <button
                    className="p-2 text-xl bg-blue-500 text-white rounded-md disabled:bg-gray-300"
                    onClick={handleFirst}
                    disabled={currentQuestionIndex === 0}
                  >
                    <FaAngleDoubleLeft />
                  </button>
                  <button
                    className="p-2 text-xl bg-blue-500 text-white rounded-md disabled:bg-gray-300"
                    onClick={handlePrevious}
                    disabled={currentQuestionIndex === 0}
                  >
                    <FaAngleLeft />
                  </button>
                  <div className="flex justify-center items-center">
                    <input
                      type="text"
                      value={inputValue}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyDown}
                      onFocus={(e) => e.target.select()}
                      className="p-2 text-center border-2 border-blue-500 mx-2 w-28 rounded-md"
                      style={{ outline: 'none', boxShadow: 'none' }}
                    />
                  </div>
                  <button
                    className="p-2 text-xl bg-blue-500 text-white rounded-md disabled:bg-gray-300"
                    onClick={handleNext}
                    disabled={currentQuestionIndex === questions.length - 1}
                  >
                    <FaAngleRight />
                  </button>
                  <button
                    className="p-2 text-xl bg-blue-500 text-white rounded-md disabled:bg-gray-300"
                    onClick={handleLast}
                    disabled={currentQuestionIndex === questions.length - 1}
                  >
                    <FaAngleDoubleRight />
                  </button>
                </div>
              </div>
            </div>
        </div>
      )}
    </div>
  );
}
