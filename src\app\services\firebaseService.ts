// Firebase Database Service
import { db } from '../config/firebase';
import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp,
  doc,
  setDoc,
  updateDoc,
  increment,
  getDoc,
  onSnapshot
} from 'firebase/firestore';

// Collection names
export const COLLECTIONS = {
  STATISTICS: 'statistics',
  PAGE_VIEWS: 'pageViews',
  TEST_SESSIONS: 'testSessions',
  STUDY_SESSIONS: 'studySessions',
  MOCK_EXAM_RESULTS: 'mockExamResults',
  SYSTEM_COUNTERS: 'systemCounters'
};

// Interfaces
export interface FirebasePageView {
  page: string;
  timestamp: Timestamp;
  category?: string;
  userAgent?: string;
  ip?: string;
}

export interface FirebaseTestSession {
  type: 'mock' | 'category';
  category?: string;
  timestamp: Timestamp;
  completed: boolean;
  score?: number;
  userName?: string;
  userEmail?: string;
  duration?: number;
  questionsTotal?: number;
  correctAnswers?: number;
}

export interface FirebaseStudySession {
  category: string;
  timestamp: Timestamp;
  questionsViewed: number;
  timeSpent?: number;
  userName?: string;
}

export interface FirebaseMockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number;
  timestamp: Timestamp;
  questionsTotal: number;
  correctAnswers: number;
  completedAt: Timestamp;
  rank?: number;
}

export interface SystemCounters {
  totalPageViews: number;
  totalTestTakers: number;
  totalStudyViews: number;
  mockExamTestTakers: number;
  lastUpdated: Timestamp;
}

// Firebase Service Class
class FirebaseService {
  private isConnected = false;
  private connectionListeners: ((connected: boolean) => void)[] = [];

  constructor() {
    this.checkConnection();
  }

  // Check Firebase connection
  private async checkConnection(): Promise<void> {
    try {
      const testDoc = doc(db, 'connection', 'test');
      await getDoc(testDoc);
      this.isConnected = true;
      console.log('✅ Firebase connected successfully');
      this.notifyConnectionListeners(true);
    } catch (error) {
      this.isConnected = false;
      console.error('❌ Firebase connection failed:', error);
      this.notifyConnectionListeners(false);
    }
  }

  // Connection status
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Add connection listener
  public onConnectionChange(callback: (connected: boolean) => void): void {
    this.connectionListeners.push(callback);
  }

  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(callback => callback(connected));
  }

  // Initialize database collections and counters
  public async initializeDatabase(): Promise<void> {
    try {
      console.log('🔄 Initializing Firebase database...');
      
      // Initialize system counters
      const countersRef = doc(db, COLLECTIONS.SYSTEM_COUNTERS, 'main');
      const countersDoc = await getDoc(countersRef);
      
      if (!countersDoc.exists()) {
        await setDoc(countersRef, {
          totalPageViews: 0,
          totalTestTakers: 0,
          totalStudyViews: 0,
          mockExamTestTakers: 0,
          lastUpdated: serverTimestamp()
        });
        console.log('✅ System counters initialized');
      }

      // Create indexes (these would be set up in Firebase Console)
      console.log('📊 Database collections ready:');
      console.log(`- ${COLLECTIONS.PAGE_VIEWS}`);
      console.log(`- ${COLLECTIONS.TEST_SESSIONS}`);
      console.log(`- ${COLLECTIONS.STUDY_SESSIONS}`);
      console.log(`- ${COLLECTIONS.MOCK_EXAM_RESULTS}`);
      console.log(`- ${COLLECTIONS.SYSTEM_COUNTERS}`);
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  // Save page view
  public async savePageView(pageView: Omit<FirebasePageView, 'timestamp'>): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.PAGE_VIEWS), {
        ...pageView,
        timestamp: serverTimestamp()
      });
      
      // Update counter
      await this.incrementCounter('totalPageViews');
      
    } catch (error) {
      console.error('Error saving page view:', error);
      throw error;
    }
  }

  // Save test session
  public async saveTestSession(session: Omit<FirebaseTestSession, 'timestamp'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.TEST_SESSIONS), {
        ...session,
        timestamp: serverTimestamp()
      });
      
      // Update counters
      await this.incrementCounter('totalTestTakers');
      if (session.type === 'mock' && session.completed) {
        await this.incrementCounter('mockExamTestTakers');
      }
      
      return docRef.id;
    } catch (error) {
      console.error('Error saving test session:', error);
      throw error;
    }
  }

  // Save study session
  public async saveStudySession(session: Omit<FirebaseStudySession, 'timestamp'>): Promise<void> {
    try {
      await addDoc(collection(db, COLLECTIONS.STUDY_SESSIONS), {
        ...session,
        timestamp: serverTimestamp()
      });
      
      // Update counter
      await this.incrementCounter('totalStudyViews');
      
    } catch (error) {
      console.error('Error saving study session:', error);
      throw error;
    }
  }

  // Save mock exam result
  public async saveMockExamResult(result: Omit<FirebaseMockExamResult, 'id' | 'timestamp' | 'completedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.MOCK_EXAM_RESULTS), {
        ...result,
        timestamp: serverTimestamp(),
        completedAt: serverTimestamp()
      });
      
      console.log('✅ Mock exam result saved:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error saving mock exam result:', error);
      throw error;
    }
  }

  // Get mock exam leaderboard
  public async getMockExamLeaderboard(limitCount: number = 10): Promise<FirebaseMockExamResult[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.MOCK_EXAM_RESULTS),
        orderBy('score', 'desc'),
        orderBy('duration', 'asc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const results: FirebaseMockExamResult[] = [];

      let index = 0;
      querySnapshot.forEach((doc) => {
        results.push({
          id: doc.id,
          rank: index + 1,
          ...doc.data()
        } as FirebaseMockExamResult);
        index++;
      });
      
      return results;
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      throw error;
    }
  }

  // Get system counters
  public async getSystemCounters(): Promise<SystemCounters> {
    try {
      const countersRef = doc(db, COLLECTIONS.SYSTEM_COUNTERS, 'main');
      const countersDoc = await getDoc(countersRef);
      
      if (countersDoc.exists()) {
        return countersDoc.data() as SystemCounters;
      } else {
        // Initialize if doesn't exist
        await this.initializeDatabase();
        return {
          totalPageViews: 0,
          totalTestTakers: 0,
          totalStudyViews: 0,
          mockExamTestTakers: 0,
          lastUpdated: Timestamp.now()
        };
      }
    } catch (error) {
      console.error('Error getting system counters:', error);
      throw error;
    }
  }

  // Increment counter
  private async incrementCounter(counterName: keyof Omit<SystemCounters, 'lastUpdated'>): Promise<void> {
    try {
      const countersRef = doc(db, COLLECTIONS.SYSTEM_COUNTERS, 'main');
      await updateDoc(countersRef, {
        [counterName]: increment(1),
        lastUpdated: serverTimestamp()
      });
    } catch (error) {
      console.error(`Error incrementing ${counterName}:`, error);
    }
  }

  // Real-time listeners
  public onLeaderboardChange(callback: (results: FirebaseMockExamResult[]) => void): () => void {
    const q = query(
      collection(db, COLLECTIONS.MOCK_EXAM_RESULTS),
      orderBy('score', 'desc'),
      orderBy('duration', 'asc'),
      limit(10)
    );
    
    return onSnapshot(q, (snapshot) => {
      const results: FirebaseMockExamResult[] = [];
      let index = 0;
      snapshot.forEach((doc) => {
        results.push({
          id: doc.id,
          rank: index + 1,
          ...doc.data()
        } as FirebaseMockExamResult);
        index++;
      });
      callback(results);
    });
  }

  // Get all statistics
  public async getAllStatistics(): Promise<SystemCounters & { mockExamLeaderboard: FirebaseMockExamResult[] }> {
    try {
      const [counters, leaderboard] = await Promise.all([
        this.getSystemCounters(),
        this.getMockExamLeaderboard()
      ]);
      
      return {
        ...counters,
        mockExamLeaderboard: leaderboard
      };
    } catch (error) {
      console.error('Error getting all statistics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService();
export default firebaseService;
