'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { generateMockExam, getCategoryQuestions, getCategoryById, getCategoryTimeLimit, Question } from '../data';
import Timer from '../components/Timer';
import QuestionCard from '../components/QuestionCard';
import QuestionNavigation from '../components/QuestionNavigation';
import { trackTestSession } from '../utils/statistics';

function ExamContent() {
  const searchParams = useSearchParams();
  const examType = searchParams.get('type'); // 'mock' or 'category'
  const categoryId = searchParams.get('category');
  const userName = searchParams.get('name');
  const userEmail = searchParams.get('email');

  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: number }>({});
  const [timeLimit, setTimeLimit] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [examTitle, setExamTitle] = useState('');
  const [showNavigation, setShowNavigation] = useState(false);

  useEffect(() => {
    if (examType === 'mock') {
      const mockQuestions = generateMockExam();
      setQuestions(mockQuestions);
      setTimeLimit(60); // 60 minutes
      setExamTitle('ข้อสอบเสมือนจริง');
      // Note: Only track when exam is completed, not when started
    } else if (examType === 'category' && categoryId) {
      const categoryQuestions = getCategoryQuestions(categoryId);
      const category = getCategoryById(categoryId);
      const categoryTimeLimit = getCategoryTimeLimit(categoryId);

      setQuestions(categoryQuestions);
      setTimeLimit(categoryTimeLimit);
      setExamTitle(category?.name || 'ข้อสอบตามหมวดหมู่');
      // Note: Only track when exam is completed, not when started
    }
    setIsLoading(false);
  }, [examType, categoryId]);

  const handleAnswerSelect = (answerIndex: number) => {
    setAnswers({
      ...answers,
      [currentQuestionIndex]: answerIndex
    });
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleQuestionSelect = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const handleSubmitExam = () => {
    // Confirm before submitting
    const unansweredCount = questions.length - Object.keys(answers).length;
    let confirmMessage = 'คุณต้องการส่งข้อสอบหรือไม่?';

    if (unansweredCount > 0) {
      confirmMessage = `คุณยังไม่ได้ตอบ ${unansweredCount} ข้อ คุณต้องการส่งข้อสอบหรือไม่?`;
    }

    if (confirm(confirmMessage)) {
      // Calculate score for tracking
      let correctCount = 0;
      questions.forEach((question, index) => {
        if (answers[index] === question.correctAnswer) {
          correctCount++;
        }
      });
      const score = Math.round((correctCount / questions.length) * 100);

      // Track exam completion
      if (examType === 'mock') {
        trackTestSession('mock', undefined, true, score);
      } else if (examType === 'category' && categoryId) {
        trackTestSession('category', categoryId, true, score);
      }

      // Save exam data to localStorage
      const examData = {
        type: examType || '',
        category: categoryId || '',
        name: userName || '',
        email: userEmail || '',
        answers: answers,
        questions: questions,
        examTitle: examTitle,
        timeLimit: timeLimit
      };

      localStorage.setItem('examResult', JSON.stringify(examData));
      window.location.href = '/results';
    }
  };

  const handleTimeUp = () => {
    // Auto submit when time is up
    handleSubmitExam();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">กำลังเตรียมข้อสอบ...</p>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">ไม่พบข้อสอบ</h1>
        <p className="text-gray-600 mb-6">ไม่สามารถโหลดข้อสอบได้ กรุณาลองใหม่อีกครั้ง</p>
        <button
          onClick={() => window.location.href = '/'}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          กลับหน้าหลัก
        </button>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 mb-4 md:mb-6 animate-fade-in">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-xl md:text-2xl font-bold text-gray-800 mb-1">{examTitle}</h1>
            <p className="text-sm md:text-base text-gray-600">
              ผู้เข้าสอบ: <span className="font-medium">{userName}</span>
            </p>
            <p className="text-xs md:text-sm text-gray-500">
              {userEmail}
            </p>
          </div>
          <div className="flex-shrink-0">
            <Timer
              initialMinutes={timeLimit}
              onTimeUp={handleTimeUp}
            />
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:justify-between text-xs md:text-sm text-gray-600 gap-2">
          <span>ข้อ {currentQuestionIndex + 1} จาก {questions.length}</span>
          <div className="flex items-center gap-4">
            <span>ตอบแล้ว {Object.keys(answers).length} ข้อ</span>
            <button
              onClick={() => setShowNavigation(!showNavigation)}
              className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-md text-xs font-medium transition-colors"
            >
              � แผงข้อสอบ
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto">
        {/* Question Card */}
        <div>
          <QuestionCard
            question={currentQuestion}
            questionNumber={currentQuestionIndex + 1}
            selectedAnswer={answers[currentQuestionIndex]}
            onAnswerSelect={handleAnswerSelect}
          />
          
          {/* Navigation Buttons */}
          <div className="flex flex-col sm:flex-row justify-between gap-3 mt-6">
            <button
              onClick={handlePreviousQuestion}
              disabled={currentQuestionIndex === 0}
              className="bg-gray-300 text-gray-700 px-4 md:px-6 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed order-2 sm:order-1"
            >
              ข้อก่อนหน้า
            </button>

            {currentQuestionIndex === questions.length - 1 ? (
              <button
                onClick={handleSubmitExam}
                className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 md:px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-md order-1 sm:order-2"
              >
                ส่งข้อสอบ
              </button>
            ) : (
              <button
                onClick={handleNextQuestion}
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 md:px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-md order-1 sm:order-2"
              >
                ข้อถัดไป
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Question Navigation Popup */}
      {showNavigation && (
        <div className="fixed inset-0 bg-gray-300 bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-xl w-full max-h-[80vh] overflow-hidden">
            {/* Popup Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-bold text-gray-800">📋 แผงข้อสอบ</h3>
              <button
                onClick={() => setShowNavigation(false)}
                className="text-gray-500 hover:text-gray-700 text-xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                ×
              </button>
            </div>

            {/* Popup Content */}
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
              <QuestionNavigation
                totalQuestions={questions.length}
                currentQuestion={currentQuestionIndex}
                answeredQuestions={Object.keys(answers).map(Number)}
                onQuestionSelect={(index) => {
                  handleQuestionSelect(index);
                  setShowNavigation(false); // Close popup after selecting
                }}
              />

              {/* Tips */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <h4 className="font-semibold text-blue-800 mb-2 text-sm">💡 คำแนะนำ</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• อ่านคำถามให้ละเอียด</li>
                  <li>• ดูรูปภาพประกอบ (ถ้ามี)</li>
                  <li>• คลิกหมายเลขข้อเพื่อข้ามไปข้อนั้น</li>
                  <li>• ตรวจสอบคำตอบก่อนส่ง</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default function ExamPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">กำลังเตรียมข้อสอบ...</p>
        </div>
      </div>
    }>
      <ExamContent />
    </Suspense>
  );
}
