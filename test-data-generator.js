// Test Data Generator for Migration Testing
// Run this in browser console to create test data in localStorage

function generateTestData() {
  console.log('🔄 Generating test data for migration...');

  // Generate Page Views
  const pageViews = [
    { page: 'หน้าแรก', timestamp: Date.now() - 86400000, category: 'main' },
    { page: 'ข้อสอบเสมือนจริง', timestamp: Date.now() - 72000000, category: 'exam' },
    { page: 'ศึกษาข้อสอบ', timestamp: Date.now() - 36000000, category: 'study' },
    { page: 'ผลการสอบ', timestamp: Date.now() - 18000000, category: 'results' },
    { page: 'หน้าแรก', timestamp: Date.now() - 9000000, category: 'main' },
  ];

  // Generate Test Sessions
  const testSessions = [
    {
      type: 'mock',
      timestamp: Date.now() - 86400000,
      completed: true,
      score: 85,
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      duration: 1800
    },
    {
      type: 'category',
      category: 'เครื่องหมายจราจร',
      timestamp: Date.now() - 72000000,
      completed: true,
      score: 92,
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      duration: 900
    },
    {
      type: 'mock',
      timestamp: Date.now() - 36000000,
      completed: true,
      score: 78,
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      duration: 2100
    },
    {
      type: 'category',
      category: 'กฎหมายจราจร',
      timestamp: Date.now() - 18000000,
      completed: false,
      score: 0,
      userName: 'Alice Brown',
      userEmail: '<EMAIL>',
      duration: 300
    }
  ];

  // Generate Study Sessions
  const studySessions = [
    {
      category: 'เครื่องหมายจราจร',
      timestamp: Date.now() - 86400000,
      questionsViewed: 15
    },
    {
      category: 'กฎหมายจราจร',
      timestamp: Date.now() - 72000000,
      questionsViewed: 20
    },
    {
      category: 'การขับรถอย่างปลอดภัย',
      timestamp: Date.now() - 36000000,
      questionsViewed: 12
    },
    {
      category: 'จิตสำนึกและมารยาทในการขับรถ',
      timestamp: Date.now() - 18000000,
      questionsViewed: 8
    }
  ];

  // Generate Mock Exam Results
  const mockExamResults = [
    {
      id: '1',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      score: 85,
      duration: 1800,
      timestamp: Date.now() - 86400000
    },
    {
      id: '2',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      score: 92,
      duration: 1650,
      timestamp: Date.now() - 72000000
    },
    {
      id: '3',
      userName: 'Bob Wilson',
      userEmail: '<EMAIL>',
      score: 78,
      duration: 2100,
      timestamp: Date.now() - 36000000
    },
    {
      id: '4',
      userName: 'Alice Brown',
      userEmail: '<EMAIL>',
      score: 88,
      duration: 1750,
      timestamp: Date.now() - 18000000
    },
    {
      id: '5',
      userName: 'Charlie Davis',
      userEmail: '<EMAIL>',
      score: 95,
      duration: 1500,
      timestamp: Date.now() - 9000000
    }
  ];

  // Save to localStorage
  localStorage.setItem('driving_theory_page_views', JSON.stringify(pageViews));
  localStorage.setItem('driving_theory_test_sessions', JSON.stringify(testSessions));
  localStorage.setItem('driving_theory_study_sessions', JSON.stringify(studySessions));
  localStorage.setItem('mock_exam_results', JSON.stringify(mockExamResults));

  // Clear migration status to trigger migration dialog
  localStorage.removeItem('firebase_migration_completed');

  console.log('✅ Test data generated successfully!');
  console.log('📊 Data summary:');
  console.log(`- Page Views: ${pageViews.length}`);
  console.log(`- Test Sessions: ${testSessions.length}`);
  console.log(`- Study Sessions: ${studySessions.length}`);
  console.log(`- Mock Exam Results: ${mockExamResults.length}`);
  console.log('🔄 Refresh the page to see migration dialog');

  return {
    pageViews: pageViews.length,
    testSessions: testSessions.length,
    studySessions: studySessions.length,
    mockExamResults: mockExamResults.length
  };
}

function clearTestData() {
  console.log('🗑️ Clearing test data...');
  
  localStorage.removeItem('driving_theory_page_views');
  localStorage.removeItem('driving_theory_test_sessions');
  localStorage.removeItem('driving_theory_study_sessions');
  localStorage.removeItem('mock_exam_results');
  localStorage.removeItem('firebase_migration_completed');
  
  // Clear any migration backups
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('migration_backup_')) {
      localStorage.removeItem(key);
    }
  });

  console.log('✅ Test data cleared!');
  console.log('🔄 Refresh the page to reset');
}

function checkCurrentData() {
  console.log('📊 Current localStorage data:');
  
  const pageViews = JSON.parse(localStorage.getItem('driving_theory_page_views') || '[]');
  const testSessions = JSON.parse(localStorage.getItem('driving_theory_test_sessions') || '[]');
  const studySessions = JSON.parse(localStorage.getItem('driving_theory_study_sessions') || '[]');
  const mockResults = JSON.parse(localStorage.getItem('mock_exam_results') || '[]');
  const migrationStatus = JSON.parse(localStorage.getItem('firebase_migration_completed') || 'null');

  console.log(`- Page Views: ${pageViews.length}`);
  console.log(`- Test Sessions: ${testSessions.length}`);
  console.log(`- Study Sessions: ${studySessions.length}`);
  console.log(`- Mock Exam Results: ${mockResults.length}`);
  console.log(`- Migration Completed: ${migrationStatus ? 'Yes' : 'No'}`);

  return {
    pageViews: pageViews.length,
    testSessions: testSessions.length,
    studySessions: studySessions.length,
    mockExamResults: mockResults.length,
    migrationCompleted: !!migrationStatus
  };
}

// Make functions available globally
window.generateTestData = generateTestData;
window.clearTestData = clearTestData;
window.checkCurrentData = checkCurrentData;

console.log('🔧 Migration Test Tools Loaded!');
console.log('Available functions:');
console.log('- generateTestData() - Create test data for migration');
console.log('- clearTestData() - Clear all test data');
console.log('- checkCurrentData() - Check current data status');
console.log('');
console.log('💡 Usage:');
console.log('1. Run generateTestData() to create test data');
console.log('2. Refresh page to see migration dialog');
console.log('3. Test migration process');
console.log('4. Use clearTestData() to reset');
