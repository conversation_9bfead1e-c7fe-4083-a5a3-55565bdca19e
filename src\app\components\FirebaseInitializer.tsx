'use client';

import { useEffect, useState } from 'react';
import firebaseService from '../services/firebaseService';

interface FirebaseInitializerProps {
  children: React.ReactNode;
}

export default function FirebaseInitializer({ children }: FirebaseInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        console.log('🔄 Initializing Firebase...');
        
        // Initialize the database
        await firebaseService.initializeDatabase();
        
        // Check connection status
        const connectionStatus = firebaseService.getConnectionStatus();
        setIsConnected(connectionStatus);
        
        // Listen for connection changes
        firebaseService.onConnectionChange((connected) => {
          setIsConnected(connected);
          if (connected) {
            console.log('✅ Firebase connected');
            setError(null);
          } else {
            console.log('❌ Firebase disconnected');
            setError('Firebase connection lost. Using offline mode.');
          }
        });
        
        setIsInitialized(true);
        console.log('✅ Firebase initialization complete');
        
      } catch (err) {
        console.error('❌ Firebase initialization failed:', err);
        setError('Failed to initialize Firebase. Running in offline mode.');
        setIsInitialized(true); // Still allow app to run
      }
    };

    initializeFirebase();
  }, []);

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">กำลังเตรียมระบบ</h2>
          <p className="text-gray-600">กำลังเชื่อมต่อฐานข้อมูล...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Connection Status Indicator */}
      {error && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Connection Status Badge */}
      <div className="fixed top-4 right-4 z-50">
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
          isConnected 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {isConnected ? '🟢 Online' : '🔴 Offline'}
        </div>
      </div>

      {children}
    </>
  );
}
