'use client';

interface QuestionNavigationProps {
  totalQuestions: number;
  currentQuestion: number;
  answeredQuestions: number[];
  onQuestionSelect: (index: number) => void;
}

export default function QuestionNavigation({
  totalQuestions,
  currentQuestion,
  answeredQuestions,
  onQuestionSelect
}: QuestionNavigationProps) {
  
  const getQuestionStatus = (index: number) => {
    if (index === currentQuestion) {
      return 'current';
    } else if (answeredQuestions.includes(index)) {
      return 'answered';
    } else {
      return 'unanswered';
    }
  };

  const getButtonClass = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-blue-600 text-white border-blue-600';
      case 'answered':
        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';
      case 'unanswered':
        return 'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-300';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        ข้อสอบทั้งหมด
      </h3>
      
      {/* Legend */}
      <div className="mb-4 space-y-2 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-blue-600 rounded"></div>
          <span className="text-gray-600">ข้อปัจจุบัน</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
          <span className="text-gray-600">ตอบแล้ว</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
          <span className="text-gray-600">ยังไม่ตอบ</span>
        </div>
      </div>

      {/* Question Grid */}
      <div className="grid grid-cols-5 gap-2">
        {Array.from({ length: totalQuestions }, (_, index) => {
          const status = getQuestionStatus(index);
          return (
            <button
              key={index}
              onClick={() => onQuestionSelect(index)}
              className={`
                w-10 h-10 rounded-lg border-2 text-sm font-semibold
                transition-all duration-200 hover:scale-105
                ${getButtonClass(status)}
              `}
              title={`ข้อ ${index + 1} - ${
                status === 'current' ? 'ข้อปัจจุบัน' :
                status === 'answered' ? 'ตอบแล้ว' : 'ยังไม่ตอบ'
              }`}
            >
              {index + 1}
            </button>
          );
        })}
      </div>

      {/* Summary */}
      <div className="">
        {/* mt-6 p-4 bg-gray-50 rounded-lg */}
        {/* <div className="text-sm space-y-1">
          <div className="flex justify-between">
            <span className="text-gray-600">ข้อสอบทั้งหมด:</span>
            <span className="font-semibold">{totalQuestions} ข้อ</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">ตอบแล้ว:</span>
            <span className="font-semibold text-green-600">
              {answeredQuestions.length} ข้อ
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">ยังไม่ตอบ:</span>
            <span className="font-semibold text-red-600">
              {totalQuestions - answeredQuestions.length} ข้อ
            </span>
          </div>
        </div> */}

        {/* Progress */}
        {/* <div className="mt-3">
          <div className="flex justify-between text-xs text-gray-600 mb-1">
            <span>ความคืบหน้า</span>
            <span className="font-semibold">{Math.round((answeredQuestions.length / totalQuestions) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500 flex items-center justify-end pr-2"
              style={{ width: `${(answeredQuestions.length / totalQuestions) * 100}%` }}
            >
              {answeredQuestions.length > 0 && (
                <span className="text-white text-xs font-bold">
                  {Math.round((answeredQuestions.length / totalQuestions) * 100)}%
                </span>
              )}
            </div>
          </div>
        </div> */}

        {/* Completion Status */}
        {/* {answeredQuestions.length === totalQuestions && (
          <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-lg text-center">
            <span className="text-green-800 text-sm font-medium">
              🎉 ตอบครบทุกข้อแล้ว!
            </span>
          </div>
        )} */}
      </div>
    </div>
  );
}
